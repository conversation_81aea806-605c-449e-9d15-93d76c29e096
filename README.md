# Linux远程基线核查工具

一个基于Tauri框架开发的跨平台Linux远程基线核查工具，支持SSH连接到Linux服务器并执行安全基线核查，提供直观的结果展示和数据持久化功能。

## 🚀 项目特性

- **跨平台支持**: 基于Tauri框架，支持Windows、macOS、Linux
- **远程连接**: 支持SSH密码和密钥认证方式
- **基线核查**: 内置CIS基线规则，支持自定义规则
- **实时监控**: 任务进度实时显示，支持任务取消
- **数据持久化**: SQLite本地数据库存储
- **结果分析**: 详细的核查结果展示和统计分析
- **数据导出**: 支持多种格式的报告导出

## 🏗️ 技术架构

### 核心技术栈
- **应用框架**: Tauri 1.5+
- **前端**: React 18 + TypeScript + Ant Design
- **后端**: Rust 1.70+
- **数据库**: SQLite 3.40+
- **构建工具**: Vite + pnpm

### 系统架构
```
┌─────────────────────────────────────────────────────────────┐
│                    Tauri Application                        │
├─────────────────────────────────────────────────────────────┤
│  Frontend (React + TypeScript)                             │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐│
│  │   Connection    │ │   Baseline      │ │    Results      ││
│  │   Management    │ │   Checker       │ │    Display      ││
│  └─────────────────┘ └─────────────────┘ └─────────────────┘│
├─────────────────────────────────────────────────────────────┤
│  Tauri Commands (Rust Backend)                             │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐│
│  │   SSH Client    │ │   Baseline      │ │   Database      ││
│  │   Module        │ │   Engine        │ │   Manager       ││
│  └─────────────────┘ └─────────────────┘ └─────────────────┘│
├─────────────────────────────────────────────────────────────┤
│  Data Layer (SQLite + Config + Logs)                       │
└─────────────────────────────────────────────────────────────┘
```

## 📚 文档目录

### 设计文档
1. **[项目架构设计文档](docs/01-项目架构设计文档.md)**
   - 技术选型说明
   - 系统架构设计
   - 模块划分和数据流
   - 安全性和性能设计

2. **[数据库设计文档](docs/02-数据库设计文档.md)**
   - SQLite表结构设计
   - 数据关系图
   - 索引和优化策略
   - 初始化数据

3. **[API接口设计文档](docs/03-API接口设计文档.md)**
   - Tauri Commands定义
   - 数据模型设计
   - 错误处理机制
   - 事件系统设计

4. **[开发环境搭建指南](docs/04-开发环境搭建指南.md)**
   - 依赖安装步骤
   - 项目初始化
   - 开发工具配置
   - 常见问题解决

5. **[功能模块设计文档](docs/05-功能模块设计文档.md)**
   - 核心功能模块详细设计
   - 组件接口定义
   - 状态管理方案
   - 测试策略

## 🎯 第一版功能规划

### 核心功能
- [x] 远程连接管理 - SSH连接配置和管理
- [x] 基线核查引擎 - 规则执行和结果分析
- [x] 结果展示界面 - 表格展示和详情查看
- [x] 数据持久化 - SQLite数据库存储

### 基线规则支持
- **CIS Ubuntu 20.04 Benchmark** - 常用Ubuntu安全基线
- **CIS CentOS 7 Benchmark** - 常用CentOS安全基线  
- **基础安全检查** - 通用Linux安全配置检查

### 检查类别
- **系统配置** - 内核参数、系统服务配置
- **用户管理** - 用户权限、密码策略
- **网络安全** - 防火墙、网络服务配置
- **文件权限** - 关键文件和目录权限
- **服务管理** - 系统服务状态和配置

## 🚦 快速开始

### 环境要求
- **Rust**: 1.70+
- **Node.js**: 18+ LTS
- **pnpm**: 8.x+

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd baseline-checker
```

2. **安装依赖**
```bash
# 安装前端依赖
pnpm install

# Rust依赖会自动安装
```

3. **启动开发服务器**
```bash
pnpm tauri dev
```

4. **构建生产版本**
```bash
pnpm tauri build
```

### 项目结构
```
baseline-checker/
├── src-tauri/          # Rust后端代码
│   ├── src/
│   │   ├── main.rs
│   │   ├── commands/   # Tauri命令
│   │   ├── database/   # 数据库模块
│   │   ├── ssh/        # SSH客户端
│   │   └── baseline/   # 基线检查引擎
│   └── Cargo.toml
├── src/                # React前端代码
│   ├── components/     # UI组件
│   ├── pages/          # 页面组件
│   ├── hooks/          # 自定义Hooks
│   ├── stores/         # 状态管理
│   └── utils/          # 工具函数
├── docs/               # 项目文档
├── public/             # 静态资源
└── package.json
```

## 🔧 开发指南

### 代码规范
- **Rust**: 遵循官方风格指南，使用`cargo fmt`和`cargo clippy`
- **TypeScript**: 使用严格模式，遵循ESLint规则
- **提交信息**: 使用Conventional Commits规范

### 测试
```bash
# Rust单元测试
cargo test

# 前端测试
pnpm test

# 代码检查
cargo clippy
pnpm lint
```

### 调试
- 使用VS Code的Rust Analyzer扩展
- 前端使用浏览器开发者工具
- 后端日志查看：`~/.local/share/baseline-checker/logs/`

## 📊 数据库结构

### 核心表
- **servers** - 服务器信息
- **rule_sets** - 规则集定义
- **check_rules** - 检查规则
- **check_tasks** - 检查任务
- **check_results** - 检查结果
- **system_config** - 系统配置
- **operation_logs** - 操作日志

### 关系图
```
servers (1:N) check_tasks (1:N) check_results (N:1) check_rules (N:1) rule_sets
```

## 🔒 安全特性

- **连接安全**: SSH密钥认证，连接信息加密存储
- **数据安全**: 敏感数据加密，本地数据库访问控制
- **应用安全**: Tauri安全配置，CSP策略，权限最小化

## 📈 性能优化

- **前端**: 组件懒加载，虚拟滚动，状态管理优化
- **后端**: 异步处理，连接池管理，数据库索引优化
- **网络**: 连接复用，数据压缩，超时控制

## 🚀 部署

### 构建产物
- **Windows**: MSI安装包
- **macOS**: DMG镜像文件
- **Linux**: AppImage/DEB/RPM

### 自动更新
- 支持自动更新检查
- 增量更新机制
- 版本回滚支持

## 🗺️ 路线图

### v1.0 (当前版本)
- [x] 基础SSH连接功能
- [x] 内置基线规则集
- [x] 核查结果展示
- [x] SQLite数据存储

### v1.1 (计划中)
- [ ] 批量服务器检查
- [ ] 报告导出功能
- [ ] 自定义规则编辑
- [ ] 任务调度功能

### v1.2 (未来版本)
- [ ] 基线规则市场
- [ ] 团队协作功能
- [ ] API接口开放
- [ ] 插件系统

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

- 项目主页: [GitHub Repository]
- 问题反馈: [GitHub Issues]
- 文档站点: [Documentation Site]

## 🙏 致谢

感谢以下开源项目的支持：
- [Tauri](https://tauri.app/) - 跨平台应用框架
- [React](https://reactjs.org/) - 前端UI框架
- [Ant Design](https://ant.design/) - UI组件库
- [SQLite](https://sqlite.org/) - 嵌入式数据库
- [ssh2-rs](https://github.com/alexcrichton/ssh2-rs) - SSH客户端库

# Linux远程基线核查工具 - 开发环境搭建指南

## 1. 系统要求

### 1.1 支持的操作系统
- **Windows**: Windows 10 1903+ (64-bit)
- **macOS**: macOS 10.15+ (Catalina)
- **Linux**: Ubuntu 18.04+, CentOS 8+, Fedora 32+

### 1.2 硬件要求
- **内存**: 最低 4GB，推荐 8GB+
- **存储**: 最低 2GB 可用空间
- **网络**: 稳定的互联网连接（用于依赖下载）

## 2. 核心依赖安装

### 2.1 Rust 开发环境

#### Windows 安装
```powershell
# 下载并安装 Rust
# 访问 https://rustup.rs/ 下载 rustup-init.exe
# 或使用 winget
winget install Rustlang.Rustup

# 验证安装
rustc --version
cargo --version
```

#### macOS 安装
```bash
# 使用 Homebrew 安装
brew install rust

# 或使用官方安装脚本
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh

# 重新加载环境变量
source ~/.cargo/env

# 验证安装
rustc --version
cargo --version
```

#### Linux 安装
```bash
# Ubuntu/Debian
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source ~/.cargo/env

# CentOS/RHEL/Fedora
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source ~/.cargo/env

# 验证安装
rustc --version
cargo --version
```

### 2.2 Node.js 开发环境

#### 推荐版本
- **Node.js**: 18.x LTS 或 20.x LTS
- **npm**: 9.x+ 或 **pnpm**: 8.x+ (推荐)

#### Windows 安装
```powershell
# 使用 winget
winget install OpenJS.NodeJS.LTS

# 或下载安装包
# 访问 https://nodejs.org/ 下载 LTS 版本

# 安装 pnpm
npm install -g pnpm

# 验证安装
node --version
pnpm --version
```

#### macOS 安装
```bash
# 使用 Homebrew
brew install node pnpm

# 或使用 nvm
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install --lts
nvm use --lts

# 安装 pnpm
npm install -g pnpm

# 验证安装
node --version
pnpm --version
```

#### Linux 安装
```bash
# Ubuntu/Debian - 使用 NodeSource 仓库
curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -
sudo apt-get install -y nodejs

# CentOS/RHEL/Fedora
curl -fsSL https://rpm.nodesource.com/setup_lts.x | sudo bash -
sudo dnf install -y nodejs npm

# 安装 pnpm
npm install -g pnpm

# 验证安装
node --version
pnpm --version
```

### 2.3 系统依赖

#### Windows
```powershell
# 安装 Visual Studio Build Tools
# 下载并安装 Visual Studio Installer
# 选择 "C++ build tools" 工作负载

# 或使用 winget 安装
winget install Microsoft.VisualStudio.2022.BuildTools

# 安装 Git
winget install Git.Git
```

#### macOS
```bash
# 安装 Xcode Command Line Tools
xcode-select --install

# 安装 Git (通常已包含在 Xcode Tools 中)
git --version
```

#### Linux
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install -y build-essential pkg-config libssl-dev libsqlite3-dev git

# CentOS/RHEL/Fedora
sudo dnf groupinstall -y "Development Tools"
sudo dnf install -y pkg-config openssl-devel sqlite-devel git
```

## 3. 项目初始化

### 3.1 创建 Tauri 项目

```bash
# 安装 Tauri CLI
cargo install tauri-cli

# 创建新项目
cargo tauri init

# 或使用 create-tauri-app (推荐)
pnpm create tauri-app baseline-checker
cd baseline-checker
```

### 3.2 项目结构设置

```bash
# 项目目录结构
baseline-checker/
├── src-tauri/          # Rust 后端代码
│   ├── src/
│   │   ├── main.rs
│   │   ├── commands/   # Tauri 命令
│   │   ├── database/   # 数据库模块
│   │   ├── ssh/        # SSH 客户端
│   │   └── baseline/   # 基线检查引擎
│   ├── Cargo.toml
│   └── tauri.conf.json
├── src/                # React 前端代码
│   ├── components/
│   ├── pages/
│   ├── hooks/
│   ├── stores/
│   └── utils/
├── public/
├── docs/               # 文档目录
├── package.json
├── vite.config.ts
└── README.md
```

### 3.3 配置文件设置

#### package.json
```json
{
  "name": "baseline-checker",
  "version": "1.0.0",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build",
    "preview": "vite preview",
    "tauri": "tauri"
  },
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "@tauri-apps/api": "^1.5.0",
    "antd": "^5.12.0",
    "zustand": "^4.4.0",
    "@ant-design/icons": "^5.2.0",
    "dayjs": "^1.11.0"
  },
  "devDependencies": {
    "@types/react": "^18.2.0",
    "@types/react-dom": "^18.2.0",
    "@vitejs/plugin-react": "^4.0.0",
    "typescript": "^5.0.0",
    "vite": "^5.0.0",
    "@tauri-apps/cli": "^1.5.0"
  }
}
```

#### Cargo.toml (src-tauri/Cargo.toml)
```toml
[package]
name = "baseline-checker"
version = "1.0.0"
edition = "2021"

[build-dependencies]
tauri-build = { version = "1.5", features = [] }

[dependencies]
tauri = { version = "1.5", features = ["api-all"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tokio = { version = "1.0", features = ["full"] }
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "sqlite", "chrono", "uuid"] }
ssh2 = "0.9"
chrono = { version = "0.4", features = ["serde"] }
uuid = { version = "1.0", features = ["v4", "serde"] }
anyhow = "1.0"
thiserror = "1.0"
log = "0.4"
env_logger = "0.10"
```

## 4. 开发工具配置

### 4.1 VS Code 配置

#### 推荐扩展
```json
// .vscode/extensions.json
{
  "recommendations": [
    "rust-lang.rust-analyzer",
    "tauri-apps.tauri-vscode",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-typescript-next"
  ]
}
```

#### 工作区设置
```json
// .vscode/settings.json
{
  "rust-analyzer.linkedProjects": ["./src-tauri/Cargo.toml"],
  "typescript.preferences.importModuleSpecifier": "relative",
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  }
}
```

### 4.2 代码格式化配置

#### Prettier 配置
```json
// .prettierrc
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false
}
```

#### ESLint 配置
```json
// .eslintrc.json
{
  "extends": [
    "eslint:recommended",
    "@typescript-eslint/recommended",
    "plugin:react/recommended",
    "plugin:react-hooks/recommended"
  ],
  "parser": "@typescript-eslint/parser",
  "plugins": ["@typescript-eslint", "react", "react-hooks"],
  "rules": {
    "react/react-in-jsx-scope": "off"
  },
  "settings": {
    "react": {
      "version": "detect"
    }
  }
}
```

## 5. 项目启动

### 5.1 安装依赖

```bash
# 进入项目目录
cd baseline-checker

# 安装前端依赖
pnpm install

# 安装 Rust 依赖 (自动执行)
cd src-tauri
cargo build
cd ..
```

### 5.2 开发模式启动

```bash
# 启动开发服务器
pnpm tauri dev

# 或分别启动
# 终端1: 启动前端开发服务器
pnpm dev

# 终端2: 启动 Tauri 开发模式
pnpm tauri dev
```

### 5.3 构建生产版本

```bash
# 构建生产版本
pnpm tauri build

# 构建产物位置:
# Windows: src-tauri/target/release/bundle/msi/
# macOS: src-tauri/target/release/bundle/dmg/
# Linux: src-tauri/target/release/bundle/appimage/
```

## 6. 数据库初始化

### 6.1 SQLite 数据库设置

```bash
# 创建数据库目录
mkdir -p src-tauri/database

# 创建初始化脚本
touch src-tauri/database/init.sql
```

### 6.2 数据库迁移

```rust
// src-tauri/src/database/mod.rs
use sqlx::{SqlitePool, migrate::MigrateDatabase};

pub async fn init_database() -> Result<SqlitePool, sqlx::Error> {
    let database_url = "sqlite:./database/baseline_checker.db";
    
    if !sqlx::Sqlite::database_exists(database_url).await.unwrap_or(false) {
        sqlx::Sqlite::create_database(database_url).await?;
    }
    
    let pool = SqlitePool::connect(database_url).await?;
    
    // 运行迁移
    sqlx::migrate!("./migrations").run(&pool).await?;
    
    Ok(pool)
}
```

## 7. 常见问题解决

### 7.1 Rust 编译问题

```bash
# 更新 Rust 工具链
rustup update

# 清理构建缓存
cargo clean

# 重新构建
cargo build
```

### 7.2 Node.js 依赖问题

```bash
# 清理 node_modules
rm -rf node_modules pnpm-lock.yaml

# 重新安装
pnpm install
```

### 7.3 Tauri 构建问题

```bash
# 更新 Tauri CLI
cargo install tauri-cli --force

# 检查 Tauri 配置
pnpm tauri info
```

### 7.4 平台特定问题

#### Windows
- 确保安装了 Visual Studio Build Tools
- 检查 Windows SDK 版本
- 使用管理员权限运行命令

#### macOS
- 确保 Xcode Command Line Tools 已安装
- 检查 macOS 版本兼容性
- 处理代码签名问题

#### Linux
- 安装必要的系统库
- 检查 AppImage 运行时依赖
- 处理权限问题

## 8. 开发流程

### 8.1 Git 工作流

```bash
# 克隆项目
git clone <repository-url>
cd baseline-checker

# 创建功能分支
git checkout -b feature/new-feature

# 提交更改
git add .
git commit -m "feat: add new feature"

# 推送分支
git push origin feature/new-feature
```

### 8.2 代码质量检查

```bash
# Rust 代码检查
cargo clippy
cargo fmt --check

# 前端代码检查
pnpm lint
pnpm type-check

# 运行测试
cargo test
pnpm test
```

### 8.3 调试配置

```json
// .vscode/launch.json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Tauri Development Debug",
      "type": "lldb",
      "request": "launch",
      "program": "${workspaceFolder}/src-tauri/target/debug/baseline-checker",
      "args": [],
      "cwd": "${workspaceFolder}"
    }
  ]
}
```

## 9. 性能优化

### 9.1 构建优化

```toml
# src-tauri/Cargo.toml
[profile.release]
panic = "abort"
codegen-units = 1
lto = true
opt-level = "s"
strip = true
```

### 9.2 前端优化

```typescript
// vite.config.ts
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

export default defineConfig({
  plugins: [react()],
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          antd: ['antd']
        }
      }
    }
  }
});
```

## 10. 部署准备

### 10.1 环境变量配置

```bash
# .env.example
TAURI_PRIVATE_KEY=
TAURI_KEY_PASSWORD=
DATABASE_URL=sqlite:./database/baseline_checker.db
LOG_LEVEL=info
```

### 10.2 CI/CD 配置

```yaml
# .github/workflows/build.yml
name: Build and Release

on:
  push:
    tags: ['v*']

jobs:
  build:
    strategy:
      matrix:
        platform: [ubuntu-latest, windows-latest, macos-latest]
    
    runs-on: ${{ matrix.platform }}
    
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 18
      - uses: actions-rs/toolchain@v1
        with:
          toolchain: stable
      
      - run: pnpm install
      - run: pnpm tauri build
```

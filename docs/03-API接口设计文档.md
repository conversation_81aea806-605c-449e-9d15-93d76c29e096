# Linux远程基线核查工具 - API接口设计文档

## 1. 接口概述

### 1.1 通信架构
- **通信方式**: Tauri Commands (前端调用后端)
- **数据格式**: JSON
- **错误处理**: 统一错误响应格式
- **异步处理**: 支持长时间运行的任务

### 1.2 接口分类
- 服务器管理接口
- 基线核查接口
- 结果查询接口
- 系统配置接口
- 文件操作接口

## 2. 通用数据结构

### 2.1 统一响应格式

```typescript
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: string;
}
```

### 2.2 分页响应格式

```typescript
interface PagedResponse<T> {
  items: T[];
  total: number;
  page: number;
  page_size: number;
  total_pages: number;
}
```

### 2.3 通用查询参数

```typescript
interface QueryParams {
  page?: number;
  page_size?: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
  search?: string;
}
```

## 3. 服务器管理接口

### 3.1 服务器数据模型

```typescript
interface Server {
  id: number;
  name: string;
  host: string;
  port: number;
  username: string;
  auth_type: 'password' | 'key' | 'key_with_password';
  private_key_path?: string;
  description?: string;
  tags: string[];
  is_active: boolean;
  last_connected_at?: string;
  created_at: string;
  updated_at: string;
}

interface CreateServerRequest {
  name: string;
  host: string;
  port?: number;
  username: string;
  auth_type: 'password' | 'key' | 'key_with_password';
  password?: string;
  private_key_path?: string;
  description?: string;
  tags?: string[];
}
```

### 3.2 服务器管理命令

```rust
// 获取服务器列表
#[tauri::command]
async fn get_servers(params: QueryParams) -> Result<ApiResponse<PagedResponse<Server>>, String>

// 创建服务器
#[tauri::command]
async fn create_server(server: CreateServerRequest) -> Result<ApiResponse<Server>, String>

// 更新服务器
#[tauri::command]
async fn update_server(id: u32, server: CreateServerRequest) -> Result<ApiResponse<Server>, String>

// 删除服务器
#[tauri::command]
async fn delete_server(id: u32) -> Result<ApiResponse<()>, String>

// 测试服务器连接
#[tauri::command]
async fn test_server_connection(id: u32, password: Option<String>) -> Result<ApiResponse<ConnectionResult>, String>
```

### 3.3 连接测试结果

```typescript
interface ConnectionResult {
  success: boolean;
  message: string;
  server_info?: {
    os_name: string;
    os_version: string;
    kernel_version: string;
    uptime: string;
  };
  latency?: number; // 连接延迟(毫秒)
}
```

## 4. 基线核查接口

### 4.1 规则集数据模型

```typescript
interface RuleSet {
  id: number;
  name: string;
  description: string;
  version: string;
  category: 'security' | 'compliance' | 'performance' | 'custom';
  is_builtin: boolean;
  is_active: boolean;
  rule_count: number;
  created_at: string;
  updated_at: string;
}

interface CheckRule {
  id: number;
  rule_set_id: number;
  rule_code: string;
  name: string;
  category: 'system' | 'network' | 'service' | 'file' | 'user';
  severity: 'low' | 'medium' | 'high' | 'critical';
  command: string;
  expected_type: 'exact' | 'contains' | 'regex' | 'numeric';
  expected_value: string;
  description: string;
  remediation: string;
  reference_links: Array<{title: string, url: string}>;
  is_active: boolean;
  execution_timeout: number;
}
```

### 4.2 核查任务数据模型

```typescript
interface CheckTask {
  id: number;
  server_id: number;
  rule_set_id: number;
  task_name: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  total_rules: number;
  completed_rules: number;
  passed_rules: number;
  failed_rules: number;
  error_message?: string;
  started_at?: string;
  completed_at?: string;
  created_at: string;
}

interface CreateCheckTaskRequest {
  server_id: number;
  rule_set_id: number;
  task_name?: string;
  selected_rules?: number[]; // 可选择特定规则
}
```

### 4.3 基线核查命令

```rust
// 获取规则集列表
#[tauri::command]
async fn get_rule_sets(params: QueryParams) -> Result<ApiResponse<PagedResponse<RuleSet>>, String>

// 获取规则集详情
#[tauri::command]
async fn get_rule_set_details(id: u32) -> Result<ApiResponse<RuleSet>, String>

// 获取规则列表
#[tauri::command]
async fn get_check_rules(rule_set_id: u32, params: QueryParams) -> Result<ApiResponse<PagedResponse<CheckRule>>, String>

// 创建检查任务
#[tauri::command]
async fn create_check_task(request: CreateCheckTaskRequest, password: Option<String>) -> Result<ApiResponse<CheckTask>, String>

// 获取检查任务列表
#[tauri::command]
async fn get_check_tasks(params: QueryParams) -> Result<ApiResponse<PagedResponse<CheckTask>>, String>

// 取消检查任务
#[tauri::command]
async fn cancel_check_task(task_id: u32) -> Result<ApiResponse<()>, String>

// 获取任务进度
#[tauri::command]
async fn get_task_progress(task_id: u32) -> Result<ApiResponse<TaskProgress>, String>
```

### 4.4 任务进度数据

```typescript
interface TaskProgress {
  task_id: number;
  status: string;
  total_rules: number;
  completed_rules: number;
  current_rule?: {
    rule_code: string;
    rule_name: string;
  };
  estimated_remaining_time?: number; // 预估剩余时间(秒)
}
```

## 5. 结果查询接口

### 5.1 检查结果数据模型

```typescript
interface CheckResult {
  id: number;
  task_id: number;
  rule_id: number;
  rule_code: string;
  rule_name: string;
  category: string;
  severity: string;
  status: 'passed' | 'failed' | 'error' | 'skipped';
  actual_value?: string;
  expected_value?: string;
  command_output?: string;
  error_message?: string;
  execution_time: number;
  created_at: string;
}

interface TaskSummary {
  task_id: number;
  server_name: string;
  rule_set_name: string;
  total_rules: number;
  passed_rules: number;
  failed_rules: number;
  error_rules: number;
  skipped_rules: number;
  pass_rate: number;
  risk_score: number;
  completed_at: string;
}
```

### 5.2 结果查询命令

```rust
// 获取检查结果列表
#[tauri::command]
async fn get_check_results(task_id: u32, params: QueryParams) -> Result<ApiResponse<PagedResponse<CheckResult>>, String>

// 获取任务摘要
#[tauri::command]
async fn get_task_summary(task_id: u32) -> Result<ApiResponse<TaskSummary>, String>

// 获取结果统计
#[tauri::command]
async fn get_results_statistics(server_id: Option<u32>, date_range: Option<DateRange>) -> Result<ApiResponse<ResultsStatistics>, String>

// 导出检查结果
#[tauri::command]
async fn export_check_results(task_id: u32, format: ExportFormat) -> Result<ApiResponse<ExportResult>, String>
```

### 5.3 统计数据模型

```typescript
interface ResultsStatistics {
  total_tasks: number;
  completed_tasks: number;
  failed_tasks: number;
  average_pass_rate: number;
  severity_distribution: {
    critical: number;
    high: number;
    medium: number;
    low: number;
  };
  category_distribution: {
    [category: string]: number;
  };
  trend_data: Array<{
    date: string;
    pass_rate: number;
    task_count: number;
  }>;
}

interface DateRange {
  start_date: string;
  end_date: string;
}

type ExportFormat = 'json' | 'csv' | 'xlsx' | 'pdf';

interface ExportResult {
  file_path: string;
  file_size: number;
  export_time: string;
}
```

## 6. 系统配置接口

### 6.1 配置数据模型

```typescript
interface SystemConfig {
  ssh_timeout: number;
  max_concurrent_checks: number;
  auto_save_results: boolean;
  log_level: 'debug' | 'info' | 'warn' | 'error';
  theme: 'light' | 'dark' | 'auto';
  language: string;
  export_path: string;
}

interface AppInfo {
  version: string;
  build_date: string;
  platform: string;
  data_path: string;
  log_path: string;
}
```

### 6.2 配置管理命令

```rust
// 获取系统配置
#[tauri::command]
async fn get_system_config() -> Result<ApiResponse<SystemConfig>, String>

// 更新系统配置
#[tauri::command]
async fn update_system_config(config: SystemConfig) -> Result<ApiResponse<()>, String>

// 获取应用信息
#[tauri::command]
async fn get_app_info() -> Result<ApiResponse<AppInfo>, String>

// 重置配置
#[tauri::command]
async fn reset_system_config() -> Result<ApiResponse<()>, String>
```

## 7. 文件操作接口

### 7.1 文件操作命令

```rust
// 选择私钥文件
#[tauri::command]
async fn select_private_key_file() -> Result<ApiResponse<String>, String>

// 选择导出目录
#[tauri::command]
async fn select_export_directory() -> Result<ApiResponse<String>, String>

// 获取日志文件内容
#[tauri::command]
async fn get_log_content(lines: Option<u32>) -> Result<ApiResponse<String>, String>

// 清理日志文件
#[tauri::command]
async fn clear_logs() -> Result<ApiResponse<()>, String>
```

## 8. 事件系统

### 8.1 事件类型

```typescript
// 任务进度更新事件
interface TaskProgressEvent {
  task_id: number;
  progress: TaskProgress;
}

// 连接状态变化事件
interface ConnectionStatusEvent {
  server_id: number;
  status: 'connected' | 'disconnected' | 'error';
  message?: string;
}

// 系统通知事件
interface SystemNotificationEvent {
  type: 'info' | 'warning' | 'error' | 'success';
  title: string;
  message: string;
  duration?: number;
}
```

### 8.2 事件监听

```typescript
// 前端事件监听示例
import { listen } from '@tauri-apps/api/event';

// 监听任务进度更新
listen<TaskProgressEvent>('task-progress', (event) => {
  console.log('Task progress:', event.payload);
});

// 监听连接状态变化
listen<ConnectionStatusEvent>('connection-status', (event) => {
  console.log('Connection status:', event.payload);
});

// 监听系统通知
listen<SystemNotificationEvent>('system-notification', (event) => {
  console.log('System notification:', event.payload);
});
```

## 9. 错误处理

### 9.1 错误代码定义

```typescript
enum ErrorCode {
  // 通用错误
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
  INVALID_PARAMETER = 'INVALID_PARAMETER',
  
  // 数据库错误
  DATABASE_ERROR = 'DATABASE_ERROR',
  RECORD_NOT_FOUND = 'RECORD_NOT_FOUND',
  DUPLICATE_RECORD = 'DUPLICATE_RECORD',
  
  // 网络连接错误
  CONNECTION_FAILED = 'CONNECTION_FAILED',
  CONNECTION_TIMEOUT = 'CONNECTION_TIMEOUT',
  AUTHENTICATION_FAILED = 'AUTHENTICATION_FAILED',
  
  // 任务执行错误
  TASK_CREATION_FAILED = 'TASK_CREATION_FAILED',
  TASK_EXECUTION_FAILED = 'TASK_EXECUTION_FAILED',
  TASK_CANCELLED = 'TASK_CANCELLED',
  
  // 文件操作错误
  FILE_NOT_FOUND = 'FILE_NOT_FOUND',
  FILE_ACCESS_DENIED = 'FILE_ACCESS_DENIED',
  EXPORT_FAILED = 'EXPORT_FAILED'
}
```

### 9.2 错误处理示例

```rust
// Rust后端错误处理
use serde::{Deserialize, Serialize};

#[derive(Serialize, Deserialize)]
struct ApiError {
    code: String,
    message: String,
    details: Option<serde_json::Value>,
}

impl From<sqlx::Error> for ApiError {
    fn from(err: sqlx::Error) -> Self {
        ApiError {
            code: "DATABASE_ERROR".to_string(),
            message: err.to_string(),
            details: None,
        }
    }
}
```

## 10. 接口测试

### 10.1 测试用例

```typescript
// 接口测试示例
describe('Server Management API', () => {
  test('should create server successfully', async () => {
    const server = {
      name: 'Test Server',
      host: '*************',
      username: 'root',
      auth_type: 'password' as const
    };
    
    const response = await invoke('create_server', { server });
    expect(response.success).toBe(true);
    expect(response.data.name).toBe('Test Server');
  });
});
```

### 10.2 性能要求

- 服务器列表查询: < 100ms
- 连接测试: < 5s
- 基线核查: 根据规则数量，平均每规则 < 2s
- 结果查询: < 200ms
- 数据导出: < 10s (1000条记录)

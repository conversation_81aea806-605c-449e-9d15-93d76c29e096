# Linux远程基线核查工具 - 功能模块设计文档

## 1. 模块概述

### 1.1 核心功能模块
1. **连接管理模块** - 服务器连接配置和SSH连接管理
2. **基线引擎模块** - 核查规则执行和结果分析
3. **数据管理模块** - 数据库操作和数据持久化
4. **用户界面模块** - 前端交互和数据展示
5. **配置管理模块** - 系统配置和用户偏好管理

### 1.2 辅助功能模块
1. **日志管理模块** - 系统日志记录和查看
2. **文件操作模块** - 文件选择和导出功能
3. **通知系统模块** - 系统通知和消息提示
4. **安全管理模块** - 数据加密和安全控制

## 2. 连接管理模块

### 2.1 模块职责
- SSH连接建立和管理
- 服务器信息存储和验证
- 连接状态监控和重连机制
- 认证方式支持（密码、密钥）

### 2.2 核心组件

#### 2.2.1 SSH客户端 (SSHClient)
```rust
// src-tauri/src/ssh/client.rs
use ssh2::Session;
use std::net::TcpStream;

pub struct SSHClient {
    session: Option<Session>,
    host: String,
    port: u16,
    username: String,
    auth_type: AuthType,
}

pub enum AuthType {
    Password(String),
    PrivateKey { path: String, passphrase: Option<String> },
}

impl SSHClient {
    pub fn new(host: String, port: u16, username: String, auth_type: AuthType) -> Self;
    pub async fn connect(&mut self) -> Result<(), SSHError>;
    pub async fn execute_command(&self, command: &str) -> Result<CommandResult, SSHError>;
    pub async fn disconnect(&mut self);
    pub fn is_connected(&self) -> bool;
}

pub struct CommandResult {
    pub stdout: String,
    pub stderr: String,
    pub exit_code: i32,
    pub execution_time: u64,
}
```

#### 2.2.2 连接管理器 (ConnectionManager)
```rust
// src-tauri/src/ssh/manager.rs
use std::collections::HashMap;
use tokio::sync::RwLock;

pub struct ConnectionManager {
    connections: RwLock<HashMap<u32, SSHClient>>,
    connection_pool_size: usize,
}

impl ConnectionManager {
    pub fn new(pool_size: usize) -> Self;
    pub async fn get_connection(&self, server_id: u32) -> Result<&SSHClient, SSHError>;
    pub async fn create_connection(&self, server: &Server, password: Option<String>) -> Result<u32, SSHError>;
    pub async fn test_connection(&self, server: &Server, password: Option<String>) -> Result<ConnectionResult, SSHError>;
    pub async fn close_connection(&self, server_id: u32);
    pub async fn close_all_connections(&self);
}
```

### 2.3 前端组件

#### 2.3.1 服务器管理组件
```typescript
// src/components/ServerManagement.tsx
interface ServerManagementProps {
  onServerSelect: (server: Server) => void;
}

export const ServerManagement: React.FC<ServerManagementProps> = ({ onServerSelect }) => {
  const [servers, setServers] = useState<Server[]>([]);
  const [showAddModal, setShowAddModal] = useState(false);
  
  // 服务器列表展示
  // 添加/编辑/删除服务器
  // 连接测试功能
  // 批量操作支持
};
```

#### 2.3.2 连接配置组件
```typescript
// src/components/ConnectionConfig.tsx
interface ConnectionConfigProps {
  server?: Server;
  onSave: (server: CreateServerRequest) => void;
  onCancel: () => void;
}

export const ConnectionConfig: React.FC<ConnectionConfigProps> = ({ server, onSave, onCancel }) => {
  // 服务器基本信息配置
  // 认证方式选择
  // 私钥文件选择
  // 连接参数设置
};
```

## 3. 基线引擎模块

### 3.1 模块职责
- 基线规则解析和执行
- 系统信息收集
- 安全配置检查
- 合规性评估和报告生成

### 3.2 核心组件

#### 3.2.1 规则引擎 (RuleEngine)
```rust
// src-tauri/src/baseline/engine.rs
pub struct RuleEngine {
    ssh_client: Arc<SSHClient>,
    rule_executor: RuleExecutor,
    result_collector: ResultCollector,
}

impl RuleEngine {
    pub fn new(ssh_client: Arc<SSHClient>) -> Self;
    pub async fn execute_rule_set(&self, rule_set: &RuleSet, progress_callback: impl Fn(TaskProgress)) -> Result<Vec<CheckResult>, BaselineError>;
    pub async fn execute_single_rule(&self, rule: &CheckRule) -> Result<CheckResult, BaselineError>;
    pub async fn collect_system_info(&self) -> Result<SystemInfo, BaselineError>;
}

pub struct SystemInfo {
    pub os_name: String,
    pub os_version: String,
    pub kernel_version: String,
    pub architecture: String,
    pub uptime: String,
    pub memory_total: u64,
    pub disk_usage: Vec<DiskInfo>,
}
```

#### 3.2.2 规则执行器 (RuleExecutor)
```rust
// src-tauri/src/baseline/executor.rs
pub struct RuleExecutor {
    timeout: Duration,
    retry_count: u32,
}

impl RuleExecutor {
    pub async fn execute(&self, rule: &CheckRule, ssh_client: &SSHClient) -> Result<ExecutionResult, ExecutionError>;
    pub fn validate_result(&self, rule: &CheckRule, output: &str) -> ValidationResult;
    pub fn calculate_risk_score(&self, results: &[CheckResult]) -> f64;
}

pub struct ExecutionResult {
    pub stdout: String,
    pub stderr: String,
    pub exit_code: i32,
    pub execution_time: u64,
}

pub enum ValidationResult {
    Passed,
    Failed { reason: String },
    Error { message: String },
}
```

#### 3.2.3 内置规则集
```rust
// src-tauri/src/baseline/builtin_rules.rs
pub struct BuiltinRules;

impl BuiltinRules {
    pub fn get_cis_ubuntu_rules() -> Vec<CheckRule>;
    pub fn get_cis_centos_rules() -> Vec<CheckRule>;
    pub fn get_basic_security_rules() -> Vec<CheckRule>;
    
    // 示例规则
    pub fn create_password_policy_rule() -> CheckRule {
        CheckRule {
            rule_code: "SEC-001".to_string(),
            name: "检查密码策略配置".to_string(),
            category: "system".to_string(),
            severity: "high".to_string(),
            command: "grep -E '^(PASS_MAX_DAYS|PASS_MIN_DAYS|PASS_WARN_AGE)' /etc/login.defs".to_string(),
            expected_type: "contains".to_string(),
            expected_value: "PASS_MAX_DAYS\t90".to_string(),
            description: "确保密码最大有效期不超过90天".to_string(),
            remediation: "编辑 /etc/login.defs 文件，设置 PASS_MAX_DAYS 90".to_string(),
            // ...
        }
    }
}
```

### 3.3 前端组件

#### 3.3.1 基线核查组件
```typescript
// src/components/BaselineChecker.tsx
export const BaselineChecker: React.FC = () => {
  const [selectedServer, setSelectedServer] = useState<Server | null>(null);
  const [selectedRuleSet, setSelectedRuleSet] = useState<RuleSet | null>(null);
  const [checkProgress, setCheckProgress] = useState<TaskProgress | null>(null);
  
  const handleStartCheck = async () => {
    // 创建检查任务
    // 监听进度更新
    // 处理检查结果
  };
  
  // 服务器选择
  // 规则集选择
  // 进度显示
  // 结果预览
};
```

#### 3.3.2 规则管理组件
```typescript
// src/components/RuleManagement.tsx
export const RuleManagement: React.FC = () => {
  const [ruleSets, setRuleSets] = useState<RuleSet[]>([]);
  const [selectedRuleSet, setSelectedRuleSet] = useState<RuleSet | null>(null);
  const [rules, setRules] = useState<CheckRule[]>([]);
  
  // 规则集列表
  // 规则详情展示
  // 规则启用/禁用
  // 自定义规则添加
};
```

## 4. 数据管理模块

### 4.1 模块职责
- SQLite数据库操作
- 数据模型定义和映射
- 查询优化和缓存
- 数据备份和恢复

### 4.2 核心组件

#### 4.2.1 数据库管理器 (DatabaseManager)
```rust
// src-tauri/src/database/manager.rs
use sqlx::{SqlitePool, Row};

pub struct DatabaseManager {
    pool: SqlitePool,
}

impl DatabaseManager {
    pub async fn new(database_url: &str) -> Result<Self, sqlx::Error>;
    pub async fn init_schema(&self) -> Result<(), sqlx::Error>;
    pub async fn backup_database(&self, backup_path: &str) -> Result<(), DatabaseError>;
    pub async fn restore_database(&self, backup_path: &str) -> Result<(), DatabaseError>;
    pub async fn optimize_database(&self) -> Result<(), sqlx::Error>;
}
```

#### 4.2.2 数据访问对象 (DAO)
```rust
// src-tauri/src/database/dao/server_dao.rs
pub struct ServerDao {
    pool: Arc<SqlitePool>,
}

impl ServerDao {
    pub async fn create(&self, server: &CreateServerRequest) -> Result<Server, sqlx::Error>;
    pub async fn get_by_id(&self, id: u32) -> Result<Option<Server>, sqlx::Error>;
    pub async fn get_all(&self, params: &QueryParams) -> Result<PagedResponse<Server>, sqlx::Error>;
    pub async fn update(&self, id: u32, server: &CreateServerRequest) -> Result<Server, sqlx::Error>;
    pub async fn delete(&self, id: u32) -> Result<(), sqlx::Error>;
    pub async fn search(&self, keyword: &str) -> Result<Vec<Server>, sqlx::Error>;
}
```

### 4.3 数据模型

#### 4.3.1 实体模型
```rust
// src-tauri/src/models/mod.rs
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

#[derive(Debug, Serialize, Deserialize, sqlx::FromRow)]
pub struct Server {
    pub id: u32,
    pub name: String,
    pub host: String,
    pub port: u16,
    pub username: String,
    pub auth_type: String,
    pub private_key_path: Option<String>,
    pub description: Option<String>,
    pub tags: String, // JSON array
    pub is_active: bool,
    pub last_connected_at: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateServerRequest {
    pub name: String,
    pub host: String,
    pub port: Option<u16>,
    pub username: String,
    pub auth_type: String,
    pub password: Option<String>,
    pub private_key_path: Option<String>,
    pub description: Option<String>,
    pub tags: Option<Vec<String>>,
}
```

## 5. 用户界面模块

### 5.1 模块职责
- 用户交互界面
- 数据可视化展示
- 响应式布局设计
- 主题和国际化支持

### 5.2 核心组件

#### 5.2.1 主布局组件
```typescript
// src/components/Layout/MainLayout.tsx
export const MainLayout: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false);
  const [currentPath, setCurrentPath] = useState('/');
  
  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider collapsible collapsed={collapsed} onCollapse={setCollapsed}>
        <NavigationMenu currentPath={currentPath} />
      </Sider>
      <Layout>
        <Header>
          <TopBar />
        </Header>
        <Content style={{ margin: '16px' }}>
          {children}
        </Content>
        <Footer>
          <StatusBar />
        </Footer>
      </Layout>
    </Layout>
  );
};
```

#### 5.2.2 结果展示组件
```typescript
// src/components/ResultDisplay.tsx
export const ResultDisplay: React.FC<{ taskId: number }> = ({ taskId }) => {
  const [results, setResults] = useState<CheckResult[]>([]);
  const [summary, setSummary] = useState<TaskSummary | null>(null);
  const [filters, setFilters] = useState<ResultFilters>({});
  
  // 结果表格展示
  // 统计图表
  // 筛选和排序
  // 详情查看
  // 导出功能
};
```

### 5.3 状态管理

#### 5.3.1 全局状态 (Zustand)
```typescript
// src/stores/appStore.ts
interface AppState {
  // 应用状态
  theme: 'light' | 'dark' | 'auto';
  language: string;
  
  // 服务器状态
  servers: Server[];
  selectedServer: Server | null;
  
  // 任务状态
  currentTask: CheckTask | null;
  taskProgress: TaskProgress | null;
  
  // 操作方法
  setTheme: (theme: 'light' | 'dark' | 'auto') => void;
  setSelectedServer: (server: Server | null) => void;
  updateTaskProgress: (progress: TaskProgress) => void;
}

export const useAppStore = create<AppState>((set) => ({
  theme: 'light',
  language: 'zh-CN',
  servers: [],
  selectedServer: null,
  currentTask: null,
  taskProgress: null,
  
  setTheme: (theme) => set({ theme }),
  setSelectedServer: (server) => set({ selectedServer: server }),
  updateTaskProgress: (progress) => set({ taskProgress: progress }),
}));
```

## 6. 配置管理模块

### 6.1 模块职责
- 系统配置读写
- 用户偏好设置
- 配置文件管理
- 默认值处理

### 6.2 核心组件

#### 6.2.1 配置管理器
```rust
// src-tauri/src/config/manager.rs
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct AppConfig {
    pub ssh_timeout: u32,
    pub max_concurrent_checks: u32,
    pub auto_save_results: bool,
    pub log_level: String,
    pub theme: String,
    pub language: String,
    pub export_path: String,
}

impl Default for AppConfig {
    fn default() -> Self {
        Self {
            ssh_timeout: 30,
            max_concurrent_checks: 5,
            auto_save_results: true,
            log_level: "info".to_string(),
            theme: "light".to_string(),
            language: "zh-CN".to_string(),
            export_path: "./exports".to_string(),
        }
    }
}

pub struct ConfigManager {
    config_path: PathBuf,
    config: RwLock<AppConfig>,
}

impl ConfigManager {
    pub fn new(config_path: PathBuf) -> Result<Self, ConfigError>;
    pub async fn load_config(&self) -> Result<AppConfig, ConfigError>;
    pub async fn save_config(&self, config: &AppConfig) -> Result<(), ConfigError>;
    pub async fn reset_to_default(&self) -> Result<(), ConfigError>;
}
```

## 7. 安全管理模块

### 7.1 模块职责
- 敏感数据加密
- 访问控制
- 安全审计
- 数据脱敏

### 7.2 核心组件

#### 7.2.1 加密管理器
```rust
// src-tauri/src/security/encryption.rs
use aes_gcm::{Aes256Gcm, Key, Nonce};

pub struct EncryptionManager {
    cipher: Aes256Gcm,
}

impl EncryptionManager {
    pub fn new(key: &[u8; 32]) -> Self;
    pub fn encrypt(&self, data: &str) -> Result<String, EncryptionError>;
    pub fn decrypt(&self, encrypted_data: &str) -> Result<String, EncryptionError>;
    pub fn hash_password(&self, password: &str) -> String;
    pub fn verify_password(&self, password: &str, hash: &str) -> bool;
}
```

## 8. 模块集成

### 8.1 依赖注入
```rust
// src-tauri/src/app.rs
pub struct Application {
    database_manager: Arc<DatabaseManager>,
    connection_manager: Arc<ConnectionManager>,
    config_manager: Arc<ConfigManager>,
    encryption_manager: Arc<EncryptionManager>,
}

impl Application {
    pub async fn new() -> Result<Self, AppError>;
    pub async fn initialize(&self) -> Result<(), AppError>;
    pub fn get_database_manager(&self) -> Arc<DatabaseManager>;
    pub fn get_connection_manager(&self) -> Arc<ConnectionManager>;
}
```

### 8.2 事件系统
```rust
// src-tauri/src/events/mod.rs
pub enum AppEvent {
    TaskProgressUpdated(TaskProgress),
    ConnectionStatusChanged(ConnectionStatus),
    SystemNotification(Notification),
}

pub struct EventBus {
    subscribers: RwLock<HashMap<String, Vec<EventHandler>>>,
}

impl EventBus {
    pub fn subscribe<F>(&self, event_type: &str, handler: F) 
    where F: Fn(&AppEvent) + Send + Sync + 'static;
    
    pub async fn publish(&self, event: AppEvent);
}
```

## 9. 错误处理

### 9.1 统一错误类型
```rust
// src-tauri/src/errors/mod.rs
use thiserror::Error;

#[derive(Error, Debug)]
pub enum AppError {
    #[error("Database error: {0}")]
    Database(#[from] sqlx::Error),
    
    #[error("SSH connection error: {0}")]
    SSH(#[from] SSHError),
    
    #[error("Configuration error: {0}")]
    Config(#[from] ConfigError),
    
    #[error("Baseline execution error: {0}")]
    Baseline(#[from] BaselineError),
}
```

## 10. 测试策略

### 10.1 单元测试
```rust
// src-tauri/src/ssh/client.rs
#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_ssh_connection() {
        // 测试SSH连接功能
    }
    
    #[tokio::test]
    async fn test_command_execution() {
        // 测试命令执行功能
    }
}
```

### 10.2 集成测试
```typescript
// src/tests/integration/baseline.test.ts
describe('Baseline Check Integration', () => {
  test('should complete full baseline check workflow', async () => {
    // 集成测试完整的基线核查流程
  });
});
```

# Linux远程基线核查工具 - 数据库设计文档

## 1. 数据库概述

### 1.1 数据库选择
- **数据库类型**: SQLite 3.40+
- **存储位置**: 用户数据目录下的 `baseline_checker.db`
- **字符编码**: UTF-8
- **连接方式**: 本地文件连接

### 1.2 设计原则
- 数据完整性保证
- 查询性能优化
- 存储空间效率
- 扩展性考虑

## 2. 数据模型设计

### 2.1 实体关系图 (ERD)

```
┌─────────────────┐    1:N    ┌─────────────────┐    1:N    ┌─────────────────┐
│    servers      │ ────────→ │   check_tasks   │ ────────→ │  check_results  │
│                 │           │                 │           │                 │
│ - id (PK)       │           │ - id (PK)       │           │ - id (PK)       │
│ - name          │           │ - server_id(FK) │           │ - task_id (FK)  │
│ - host          │           │ - rule_set_id   │           │ - rule_id (FK)  │
│ - port          │           │ - status        │           │ - status        │
│ - username      │           │ - created_at    │           │ - result_data   │
│ - auth_type     │           │ - completed_at  │           │ - created_at    │
│ - created_at    │           └─────────────────┘           └─────────────────┘
│ - updated_at    │                    │                             │
└─────────────────┘                    │ N:1                        │ N:1
                                       │                             │
                              ┌─────────────────┐                   │
                              │   rule_sets     │                   │
                              │                 │                   │
                              │ - id (PK)       │                   │
                              │ - name          │                   │
                              │ - description   │                   │
                              │ - version       │                   │
                              │ - created_at    │                   │
                              └─────────────────┘                   │
                                       │ 1:N                        │
                                       ▼                            │
                              ┌─────────────────┐ ◄─────────────────┘
                              │   check_rules   │
                              │                 │
                              │ - id (PK)       │
                              │ - rule_set_id   │
                              │ - name          │
                              │ - category      │
                              │ - severity      │
                              │ - command       │
                              │ - expected      │
                              │ - description   │
                              └─────────────────┘
```

## 3. 表结构设计

### 3.1 服务器信息表 (servers)

```sql
CREATE TABLE servers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE,
    host VARCHAR(255) NOT NULL,
    port INTEGER DEFAULT 22,
    username VARCHAR(100) NOT NULL,
    auth_type VARCHAR(20) DEFAULT 'password', -- 'password', 'key', 'key_with_password'
    private_key_path VARCHAR(500),
    description TEXT,
    tags VARCHAR(500), -- JSON array of tags
    is_active BOOLEAN DEFAULT 1,
    last_connected_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_servers_host ON servers(host);
CREATE INDEX idx_servers_name ON servers(name);
CREATE INDEX idx_servers_active ON servers(is_active);
```

### 3.2 规则集表 (rule_sets)

```sql
CREATE TABLE rule_sets (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    version VARCHAR(20) DEFAULT '1.0',
    category VARCHAR(50), -- 'security', 'compliance', 'performance', 'custom'
    is_builtin BOOLEAN DEFAULT 0,
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_rule_sets_name ON rule_sets(name);
CREATE INDEX idx_rule_sets_category ON rule_sets(category);
CREATE INDEX idx_rule_sets_active ON rule_sets(is_active);
```

### 3.3 检查规则表 (check_rules)

```sql
CREATE TABLE check_rules (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    rule_set_id INTEGER NOT NULL,
    rule_code VARCHAR(50) NOT NULL, -- 规则编码，如 SEC-001
    name VARCHAR(200) NOT NULL,
    category VARCHAR(50), -- 'system', 'network', 'service', 'file', 'user'
    severity VARCHAR(20) DEFAULT 'medium', -- 'low', 'medium', 'high', 'critical'
    command TEXT NOT NULL, -- 执行的shell命令
    expected_type VARCHAR(20) DEFAULT 'exact', -- 'exact', 'contains', 'regex', 'numeric'
    expected_value TEXT, -- 期望的结果值
    description TEXT,
    remediation TEXT, -- 修复建议
    reference_links TEXT, -- JSON array of reference URLs
    is_active BOOLEAN DEFAULT 1,
    execution_timeout INTEGER DEFAULT 30, -- 执行超时时间(秒)
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (rule_set_id) REFERENCES rule_sets(id) ON DELETE CASCADE
);

-- 索引
CREATE INDEX idx_check_rules_rule_set ON check_rules(rule_set_id);
CREATE INDEX idx_check_rules_category ON check_rules(category);
CREATE INDEX idx_check_rules_severity ON check_rules(severity);
CREATE INDEX idx_check_rules_code ON check_rules(rule_code);
CREATE UNIQUE INDEX idx_check_rules_set_code ON check_rules(rule_set_id, rule_code);
```

### 3.4 检查任务表 (check_tasks)

```sql
CREATE TABLE check_tasks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    server_id INTEGER NOT NULL,
    rule_set_id INTEGER NOT NULL,
    task_name VARCHAR(200),
    status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'running', 'completed', 'failed', 'cancelled'
    total_rules INTEGER DEFAULT 0,
    completed_rules INTEGER DEFAULT 0,
    passed_rules INTEGER DEFAULT 0,
    failed_rules INTEGER DEFAULT 0,
    error_message TEXT,
    started_at DATETIME,
    completed_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (server_id) REFERENCES servers(id) ON DELETE CASCADE,
    FOREIGN KEY (rule_set_id) REFERENCES rule_sets(id) ON DELETE CASCADE
);

-- 索引
CREATE INDEX idx_check_tasks_server ON check_tasks(server_id);
CREATE INDEX idx_check_tasks_rule_set ON check_tasks(rule_set_id);
CREATE INDEX idx_check_tasks_status ON check_tasks(status);
CREATE INDEX idx_check_tasks_created ON check_tasks(created_at);
```

### 3.5 检查结果表 (check_results)

```sql
CREATE TABLE check_results (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    task_id INTEGER NOT NULL,
    rule_id INTEGER NOT NULL,
    status VARCHAR(20) NOT NULL, -- 'passed', 'failed', 'error', 'skipped'
    actual_value TEXT, -- 实际获取的值
    expected_value TEXT, -- 期望的值
    command_output TEXT, -- 命令执行输出
    error_message TEXT, -- 错误信息
    execution_time INTEGER, -- 执行时间(毫秒)
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES check_tasks(id) ON DELETE CASCADE,
    FOREIGN KEY (rule_id) REFERENCES check_rules(id) ON DELETE CASCADE
);

-- 索引
CREATE INDEX idx_check_results_task ON check_results(task_id);
CREATE INDEX idx_check_results_rule ON check_results(rule_id);
CREATE INDEX idx_check_results_status ON check_results(status);
CREATE UNIQUE INDEX idx_check_results_task_rule ON check_results(task_id, rule_id);
```

### 3.6 系统配置表 (system_config)

```sql
CREATE TABLE system_config (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value TEXT,
    config_type VARCHAR(20) DEFAULT 'string', -- 'string', 'number', 'boolean', 'json'
    description TEXT,
    is_user_configurable BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_system_config_key ON system_config(config_key);
```

### 3.7 操作日志表 (operation_logs)

```sql
CREATE TABLE operation_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    operation_type VARCHAR(50) NOT NULL, -- 'connect', 'check', 'export', 'config'
    operation_target VARCHAR(200), -- 操作目标(服务器名、任务ID等)
    operation_result VARCHAR(20), -- 'success', 'failed', 'partial'
    details TEXT, -- JSON格式的详细信息
    user_agent VARCHAR(200),
    ip_address VARCHAR(45),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_operation_logs_type ON operation_logs(operation_type);
CREATE INDEX idx_operation_logs_created ON operation_logs(created_at);
```

## 4. 数据字典

### 4.1 枚举值定义

| 字段 | 表名 | 可选值 | 说明 |
|------|------|--------|------|
| auth_type | servers | password, key, key_with_password | 认证方式 |
| status | check_tasks | pending, running, completed, failed, cancelled | 任务状态 |
| status | check_results | passed, failed, error, skipped | 检查结果状态 |
| severity | check_rules | low, medium, high, critical | 严重程度 |
| category | rule_sets | security, compliance, performance, custom | 规则集分类 |
| category | check_rules | system, network, service, file, user | 规则分类 |
| expected_type | check_rules | exact, contains, regex, numeric | 期望值类型 |

### 4.2 JSON字段格式

#### servers.tags
```json
["web-server", "production", "centos7"]
```

#### check_rules.reference_links
```json
[
  {"title": "CIS Benchmark", "url": "https://example.com/cis"},
  {"title": "NIST Guide", "url": "https://example.com/nist"}
]
```

#### operation_logs.details
```json
{
  "server_id": 1,
  "rule_set_id": 2,
  "duration": 120,
  "rules_checked": 45,
  "rules_passed": 38,
  "rules_failed": 7
}
```

## 5. 初始化数据

### 5.1 默认规则集

```sql
-- 插入默认的安全基线规则集
INSERT INTO rule_sets (name, description, category, is_builtin, version) VALUES
('CIS Ubuntu 20.04 Benchmark', 'CIS Ubuntu Linux 20.04 LTS Benchmark v1.1.0', 'security', 1, '1.1.0'),
('CIS CentOS 7 Benchmark', 'CIS CentOS Linux 7 Benchmark v3.1.1', 'security', 1, '3.1.1'),
('Basic Security Check', '基础安全配置检查', 'security', 1, '1.0.0');
```

### 5.2 默认系统配置

```sql
-- 插入默认系统配置
INSERT INTO system_config (config_key, config_value, config_type, description) VALUES
('ssh_timeout', '30', 'number', 'SSH连接超时时间(秒)'),
('max_concurrent_checks', '5', 'number', '最大并发检查数'),
('auto_save_results', 'true', 'boolean', '自动保存检查结果'),
('log_level', 'info', 'string', '日志级别'),
('theme', 'light', 'string', '界面主题');
```

## 6. 数据库维护

### 6.1 备份策略
- 定期自动备份
- 用户手动备份
- 导出功能支持

### 6.2 清理策略
- 定期清理过期日志
- 清理无效的检查结果
- 数据库优化和重建

### 6.3 迁移策略
- 版本升级时的数据迁移
- 表结构变更处理
- 数据完整性验证

## 7. 性能优化

### 7.1 索引策略
- 主键自动索引
- 外键关联索引
- 查询频繁字段索引

### 7.2 查询优化
- 分页查询支持
- 结果缓存机制
- 批量操作优化

### 7.3 存储优化
- 数据压缩
- 归档策略
- 磁盘空间监控

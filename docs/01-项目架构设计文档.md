# Linux远程基线核查工具 - 项目架构设计文档

## 1. 项目概述

### 1.1 项目名称
Linux远程基线核查工具 (Linux Remote Baseline Checker)

### 1.2 项目目标
开发一个跨平台的GUI应用程序，用于远程连接Linux服务器并执行安全基线核查，提供直观的结果展示和数据持久化功能。

### 1.3 版本规划
- **第一版 (v1.0)**：核心功能实现，包括SSH连接、基础基线核查、结果展示和数据存储
- **后续版本**：批量检查、报告导出、自定义规则、高级分析等功能

## 2. 技术选型

### 2.1 核心技术栈

| 技术组件 | 选择方案 | 版本要求 | 选择理由 |
|---------|---------|---------|---------|
| 应用框架 | Tauri | 1.5+ | 跨平台、性能优秀、安全性高 |
| 前端框架 | React + TypeScript | React 18+ | 生态成熟、开发效率高 |
| 后端语言 | Rust | 1.70+ | 内存安全、高性能、与Tauri原生集成 |
| 数据库 | SQLite | 3.40+ | 轻量级、无需服务器、适合桌面应用 |
| UI组件库 | Ant Design | 5.0+ | 组件丰富、设计规范 |
| 状态管理 | Zustand | 4.0+ | 轻量级、易用性好 |

### 2.2 开发工具链

| 工具类型 | 选择方案 | 用途 |
|---------|---------|------|
| 构建工具 | Vite | 前端构建和开发服务器 |
| 包管理器 | pnpm | 依赖管理 |
| 代码格式化 | Prettier + ESLint | 代码质量保证 |
| 版本控制 | Git | 源码管理 |

## 3. 系统架构设计

### 3.1 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    Tauri Application                        │
├─────────────────────────────────────────────────────────────┤
│  Frontend (React + TypeScript)                             │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐│
│  │   Connection    │ │   Baseline      │ │    Results      ││
│  │   Management    │ │   Checker       │ │    Display      ││
│  │   Component     │ │   Component     │ │   Component     ││
│  └─────────────────┘ └─────────────────┘ └─────────────────┘│
├─────────────────────────────────────────────────────────────┤
│  Tauri Commands (Rust Backend)                             │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐│
│  │   SSH Client    │ │   Baseline      │ │   Database      ││
│  │   Module        │ │   Engine        │ │   Manager       ││
│  └─────────────────┘ └─────────────────┘ └─────────────────┘│
├─────────────────────────────────────────────────────────────┤
│  Data Layer                                                 │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐│
│  │   SQLite DB     │ │   Config Files  │ │   Log Files     ││
│  └─────────────────┘ └─────────────────┘ └─────────────────┘│
└─────────────────────────────────────────────────────────────┘
```

### 3.2 模块划分

#### 3.2.1 前端模块 (Frontend Modules)

1. **连接管理模块 (Connection Management)**
   - 服务器连接配置
   - SSH连接状态管理
   - 连接历史记录

2. **基线核查模块 (Baseline Checker)**
   - 核查任务配置
   - 核查进度显示
   - 核查规则管理

3. **结果展示模块 (Results Display)**
   - 核查结果表格
   - 详细信息查看
   - 数据筛选和排序

4. **系统设置模块 (System Settings)**
   - 应用配置管理
   - 主题设置
   - 日志查看

#### 3.2.2 后端模块 (Backend Modules)

1. **SSH客户端模块 (SSH Client)**
   - SSH连接建立和管理
   - 远程命令执行
   - 文件传输功能

2. **基线引擎模块 (Baseline Engine)**
   - 核查规则解析
   - 系统信息收集
   - 安全配置检查
   - 合规性评估

3. **数据库管理模块 (Database Manager)**
   - SQLite数据库操作
   - 数据模型定义
   - 查询优化

4. **配置管理模块 (Configuration Manager)**
   - 应用配置读写
   - 用户偏好设置
   - 核查规则配置

## 4. 数据流设计

### 4.1 核查流程数据流

```
用户操作 → 前端组件 → Tauri Command → SSH连接 → 远程执行 → 结果收集 → 数据库存储 → 前端展示
```

### 4.2 详细数据流程

1. **连接建立流程**
   ```
   用户输入连接信息 → 前端验证 → 调用SSH连接命令 → 建立SSH连接 → 返回连接状态
   ```

2. **基线核查流程**
   ```
   选择核查规则 → 发起核查请求 → SSH执行命令 → 收集系统信息 → 规则匹配 → 生成报告 → 存储结果
   ```

3. **结果查询流程**
   ```
   用户查询请求 → 数据库查询 → 结果格式化 → 前端展示 → 用户交互
   ```

## 5. 安全设计

### 5.1 连接安全
- SSH密钥认证支持
- 连接信息加密存储
- 会话超时管理

### 5.2 数据安全
- 敏感数据加密
- 本地数据库访问控制
- 日志脱敏处理

### 5.3 应用安全
- Tauri安全配置
- CSP策略设置
- 权限最小化原则

## 6. 性能设计

### 6.1 前端性能
- 组件懒加载
- 虚拟滚动
- 状态管理优化

### 6.2 后端性能
- 异步处理
- 连接池管理
- 数据库索引优化

### 6.3 网络性能
- 连接复用
- 数据压缩
- 超时控制

## 7. 扩展性设计

### 7.1 插件化架构
- 核查规则插件化
- 报告格式扩展
- 第三方集成接口

### 7.2 配置化设计
- 规则配置文件
- 界面布局配置
- 用户自定义设置

## 8. 部署架构

### 8.1 打包方式
- Windows: MSI安装包
- macOS: DMG镜像文件
- Linux: AppImage/DEB/RPM

### 8.2 更新机制
- 自动更新检查
- 增量更新支持
- 回滚机制

## 9. 开发规范

### 9.1 代码规范
- Rust: 遵循官方风格指南
- TypeScript: 使用严格模式
- 统一的命名约定

### 9.2 文档规范
- API文档自动生成
- 代码注释规范
- 用户手册维护

### 9.3 测试规范
- 单元测试覆盖率 > 80%
- 集成测试自动化
- 性能测试基准

## 10. 风险评估

### 10.1 技术风险
- SSH连接稳定性
- 跨平台兼容性
- 性能瓶颈

### 10.2 安全风险
- 凭据泄露
- 中间人攻击
- 本地数据安全

### 10.3 缓解措施
- 完善的错误处理
- 安全最佳实践
- 定期安全审计

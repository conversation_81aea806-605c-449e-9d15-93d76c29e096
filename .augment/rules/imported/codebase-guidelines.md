---
type: "always_apply"
---

# Linux远程基线核查工具 - 代码库指南

## 代码组织原则

### 模块化设计
- 按功能领域划分模块，每个模块职责单一
- 模块间通过明确的接口进行交互
- 避免循环依赖，保持依赖关系清晰
- 核心业务逻辑与基础设施代码分离

### 分层架构
```
Presentation Layer (前端UI)
    ↓
Application Layer (业务逻辑)
    ↓
Domain Layer (领域模型)
    ↓
Infrastructure Layer (基础设施)
```

## Rust后端代码规范

### 项目结构
```
src-tauri/src/
├── main.rs              # 应用入口点
├── lib.rs               # 库入口（如果需要）
├── commands/            # Tauri命令处理
│   ├── mod.rs
│   ├── server_commands.rs
│   ├── baseline_commands.rs
│   └── config_commands.rs
├── database/            # 数据库相关
│   ├── mod.rs
│   ├── connection.rs    # 数据库连接管理
│   ├── migrations.rs    # 数据库迁移
│   └── dao/             # 数据访问对象
│       ├── mod.rs
│       ├── server_dao.rs
│       └── task_dao.rs
├── ssh/                 # SSH客户端模块
│   ├── mod.rs
│   ├── client.rs        # SSH客户端实现
│   └── manager.rs       # 连接管理器
├── baseline/            # 基线检查引擎
│   ├── mod.rs
│   ├── engine.rs        # 规则引擎
│   ├── executor.rs      # 规则执行器
│   └── builtin_rules.rs # 内置规则
├── models/              # 数据模型
│   ├── mod.rs
│   ├── server.rs
│   ├── rule.rs
│   └── task.rs
├── config/              # 配置管理
│   ├── mod.rs
│   └── manager.rs
├── security/            # 安全模块
│   ├── mod.rs
│   └── encryption.rs
├── errors/              # 错误定义
│   ├── mod.rs
│   └── types.rs
└── utils/               # 工具函数
    ├── mod.rs
    └── helpers.rs
```

### 编码规范

#### 命名约定
```rust
// 模块名：snake_case
mod ssh_client;
mod database_manager;

// 结构体：PascalCase
struct SSHClient;
struct CheckResult;

// 函数名：snake_case
fn create_connection() -> Result<Connection, Error>;
fn execute_baseline_check() -> CheckResult;

// 常量：SCREAMING_SNAKE_CASE
const DEFAULT_SSH_PORT: u16 = 22;
const MAX_RETRY_COUNT: u32 = 3;

// 枚举：PascalCase
enum AuthType {
    Password(String),
    PrivateKey { path: String, passphrase: Option<String> },
}
```

#### 错误处理
```rust
// 使用thiserror定义错误类型
use thiserror::Error;

#[derive(Error, Debug)]
pub enum SSHError {
    #[error("Connection failed: {message}")]
    ConnectionFailed { message: String },
    
    #[error("Authentication failed")]
    AuthenticationFailed,
    
    #[error("Command execution failed: {command}")]
    CommandExecutionFailed { command: String },
}

// 函数返回Result类型
pub async fn connect_to_server(config: &ServerConfig) -> Result<SSHClient, SSHError> {
    // 实现逻辑
}
```

#### 异步编程
```rust
// 使用async/await处理异步操作
pub async fn execute_baseline_check(
    client: &SSHClient,
    rules: &[CheckRule],
) -> Result<Vec<CheckResult>, BaselineError> {
    let mut results = Vec::new();
    
    for rule in rules {
        let result = execute_single_rule(client, rule).await?;
        results.push(result);
    }
    
    Ok(results)
}

// 使用tokio::spawn处理并发
pub async fn execute_rules_concurrently(
    client: &SSHClient,
    rules: &[CheckRule],
) -> Result<Vec<CheckResult>, BaselineError> {
    let tasks: Vec<_> = rules
        .iter()
        .map(|rule| {
            let client = client.clone();
            let rule = rule.clone();
            tokio::spawn(async move {
                execute_single_rule(&client, &rule).await
            })
        })
        .collect();
    
    let mut results = Vec::new();
    for task in tasks {
        results.push(task.await??);
    }
    
    Ok(results)
}
```

#### 文档注释
```rust
/// SSH客户端，用于建立和管理SSH连接
/// 
/// # Examples
/// 
/// ```rust
/// let config = ServerConfig::new("*************", "root");
/// let mut client = SSHClient::new(config);
/// client.connect().await?;
/// ```
pub struct SSHClient {
    config: ServerConfig,
    session: Option<Session>,
}

impl SSHClient {
    /// 创建新的SSH客户端实例
    /// 
    /// # Arguments
    /// 
    /// * `config` - 服务器配置信息
    /// 
    /// # Returns
    /// 
    /// 返回SSH客户端实例
    pub fn new(config: ServerConfig) -> Self {
        Self {
            config,
            session: None,
        }
    }
    
    /// 建立SSH连接
    /// 
    /// # Errors
    /// 
    /// 当连接失败时返回`SSHError::ConnectionFailed`
    /// 当认证失败时返回`SSHError::AuthenticationFailed`
    pub async fn connect(&mut self) -> Result<(), SSHError> {
        // 实现逻辑
    }
}
```

## TypeScript前端代码规范

### 项目结构
```
src/
├── main.tsx             # 应用入口
├── App.tsx              # 根组件
├── components/          # 可复用组件
│   ├── Layout/
│   │   ├── MainLayout.tsx
│   │   ├── Sidebar.tsx
│   │   └── Header.tsx
│   ├── Server/
│   │   ├── ServerList.tsx
│   │   ├── ServerForm.tsx
│   │   └── ConnectionTest.tsx
│   ├── Baseline/
│   │   ├── RuleSetSelector.tsx
│   │   ├── CheckProgress.tsx
│   │   └── ResultDisplay.tsx
│   └── Common/
│       ├── Loading.tsx
│       ├── ErrorBoundary.tsx
│       └── ConfirmDialog.tsx
├── pages/               # 页面组件
│   ├── Dashboard.tsx
│   ├── ServerManagement.tsx
│   ├── BaselineCheck.tsx
│   └── Settings.tsx
├── hooks/               # 自定义Hooks
│   ├── useServers.ts
│   ├── useBaseline.ts
│   └── useConfig.ts
├── stores/              # 状态管理
│   ├── appStore.ts
│   ├── serverStore.ts
│   └── taskStore.ts
├── types/               # 类型定义
│   ├── server.ts
│   ├── baseline.ts
│   └── api.ts
├── utils/               # 工具函数
│   ├── api.ts
│   ├── format.ts
│   └── validation.ts
├── constants/           # 常量定义
│   ├── api.ts
│   └── config.ts
└── styles/              # 样式文件
    ├── globals.css
    └── components.css
```

### 编码规范

#### 组件定义
```typescript
// 使用函数式组件和TypeScript
interface ServerListProps {
  servers: Server[];
  onServerSelect: (server: Server) => void;
  onServerEdit: (server: Server) => void;
  onServerDelete: (serverId: number) => void;
}

export const ServerList: React.FC<ServerListProps> = ({
  servers,
  onServerSelect,
  onServerEdit,
  onServerDelete,
}) => {
  const [selectedServerId, setSelectedServerId] = useState<number | null>(null);
  
  const handleServerClick = useCallback((server: Server) => {
    setSelectedServerId(server.id);
    onServerSelect(server);
  }, [onServerSelect]);
  
  return (
    <div className="server-list">
      {servers.map((server) => (
        <ServerItem
          key={server.id}
          server={server}
          isSelected={selectedServerId === server.id}
          onClick={handleServerClick}
          onEdit={onServerEdit}
          onDelete={onServerDelete}
        />
      ))}
    </div>
  );
};
```

#### 自定义Hooks
```typescript
// 自定义Hook封装业务逻辑
export const useServers = () => {
  const [servers, setServers] = useState<Server[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const fetchServers = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await invoke<ApiResponse<Server[]>>('get_servers');
      if (response.success) {
        setServers(response.data || []);
      } else {
        setError(response.error?.message || 'Failed to fetch servers');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  }, []);
  
  const createServer = useCallback(async (serverData: CreateServerRequest) => {
    try {
      const response = await invoke<ApiResponse<Server>>('create_server', { server: serverData });
      if (response.success && response.data) {
        setServers(prev => [...prev, response.data!]);
        return response.data;
      } else {
        throw new Error(response.error?.message || 'Failed to create server');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      throw err;
    }
  }, []);
  
  useEffect(() => {
    fetchServers();
  }, [fetchServers]);
  
  return {
    servers,
    loading,
    error,
    fetchServers,
    createServer,
  };
};
```

#### 状态管理
```typescript
// 使用Zustand进行状态管理
interface AppState {
  // 应用状态
  theme: 'light' | 'dark' | 'auto';
  language: string;
  
  // 服务器状态
  servers: Server[];
  selectedServer: Server | null;
  
  // 任务状态
  currentTask: CheckTask | null;
  taskProgress: TaskProgress | null;
  
  // Actions
  setTheme: (theme: 'light' | 'dark' | 'auto') => void;
  setServers: (servers: Server[]) => void;
  setSelectedServer: (server: Server | null) => void;
  setCurrentTask: (task: CheckTask | null) => void;
  updateTaskProgress: (progress: TaskProgress) => void;
}

export const useAppStore = create<AppState>((set) => ({
  // 初始状态
  theme: 'light',
  language: 'zh-CN',
  servers: [],
  selectedServer: null,
  currentTask: null,
  taskProgress: null,
  
  // Actions
  setTheme: (theme) => set({ theme }),
  setServers: (servers) => set({ servers }),
  setSelectedServer: (server) => set({ selectedServer: server }),
  setCurrentTask: (task) => set({ currentTask: task }),
  updateTaskProgress: (progress) => set({ taskProgress: progress }),
}));
```

#### 类型定义
```typescript
// 完整的类型定义
export interface Server {
  id: number;
  name: string;
  host: string;
  port: number;
  username: string;
  auth_type: 'password' | 'key' | 'key_with_password';
  private_key_path?: string;
  description?: string;
  tags: string[];
  is_active: boolean;
  last_connected_at?: string;
  created_at: string;
  updated_at: string;
}

export interface CreateServerRequest {
  name: string;
  host: string;
  port?: number;
  username: string;
  auth_type: 'password' | 'key' | 'key_with_password';
  password?: string;
  private_key_path?: string;
  description?: string;
  tags?: string[];
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: string;
}
```

## 数据库代码规范

### 迁移文件
```sql
-- migrations/001_initial_schema.sql
-- 创建服务器表
CREATE TABLE servers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE,
    host VARCHAR(255) NOT NULL,
    port INTEGER DEFAULT 22,
    username VARCHAR(100) NOT NULL,
    auth_type VARCHAR(20) DEFAULT 'password',
    private_key_path VARCHAR(500),
    description TEXT,
    tags VARCHAR(500),
    is_active BOOLEAN DEFAULT 1,
    last_connected_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_servers_host ON servers(host);
CREATE INDEX idx_servers_name ON servers(name);
CREATE INDEX idx_servers_active ON servers(is_active);
```

### DAO实现
```rust
// 数据访问对象实现
pub struct ServerDao {
    pool: Arc<SqlitePool>,
}

impl ServerDao {
    pub fn new(pool: Arc<SqlitePool>) -> Self {
        Self { pool }
    }
    
    pub async fn create(&self, server: &CreateServerRequest) -> Result<Server, sqlx::Error> {
        let tags_json = serde_json::to_string(&server.tags.as_ref().unwrap_or(&vec![]))?;
        
        let result = sqlx::query!(
            r#"
            INSERT INTO servers (name, host, port, username, auth_type, private_key_path, description, tags)
            VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8)
            "#,
            server.name,
            server.host,
            server.port.unwrap_or(22),
            server.username,
            server.auth_type,
            server.private_key_path,
            server.description,
            tags_json
        )
        .execute(&*self.pool)
        .await?;
        
        self.get_by_id(result.last_insert_rowid() as u32).await?
            .ok_or_else(|| sqlx::Error::RowNotFound)
    }
    
    pub async fn get_by_id(&self, id: u32) -> Result<Option<Server>, sqlx::Error> {
        let server = sqlx::query_as!(
            Server,
            "SELECT * FROM servers WHERE id = ?1",
            id
        )
        .fetch_optional(&*self.pool)
        .await?;
        
        Ok(server)
    }
}
```

## 测试代码规范

### Rust单元测试
```rust
#[cfg(test)]
mod tests {
    use super::*;
    use tokio_test;
    
    #[tokio::test]
    async fn test_ssh_connection_success() {
        let config = ServerConfig {
            host: "localhost".to_string(),
            port: 22,
            username: "test".to_string(),
            auth_type: AuthType::Password("password".to_string()),
        };
        
        let mut client = SSHClient::new(config);
        let result = client.connect().await;
        
        assert!(result.is_ok());
        assert!(client.is_connected());
    }
    
    #[tokio::test]
    async fn test_command_execution() {
        let mut client = create_test_client().await;
        
        let result = client.execute_command("echo 'test'").await;
        
        assert!(result.is_ok());
        let command_result = result.unwrap();
        assert_eq!(command_result.stdout.trim(), "test");
        assert_eq!(command_result.exit_code, 0);
    }
    
    async fn create_test_client() -> SSHClient {
        // 创建测试用的SSH客户端
    }
}
```

### React组件测试
```typescript
// 组件测试
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ServerList } from './ServerList';

describe('ServerList', () => {
  const mockServers: Server[] = [
    {
      id: 1,
      name: 'Test Server 1',
      host: '*************',
      port: 22,
      username: 'root',
      auth_type: 'password',
      tags: ['test'],
      is_active: true,
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-01T00:00:00Z',
    },
  ];
  
  const mockProps = {
    servers: mockServers,
    onServerSelect: jest.fn(),
    onServerEdit: jest.fn(),
    onServerDelete: jest.fn(),
  };
  
  test('renders server list correctly', () => {
    render(<ServerList {...mockProps} />);
    
    expect(screen.getByText('Test Server 1')).toBeInTheDocument();
    expect(screen.getByText('*************')).toBeInTheDocument();
  });
  
  test('calls onServerSelect when server is clicked', async () => {
    render(<ServerList {...mockProps} />);
    
    fireEvent.click(screen.getByText('Test Server 1'));
    
    await waitFor(() => {
      expect(mockProps.onServerSelect).toHaveBeenCalledWith(mockServers[0]);
    });
  });
});
```

## 代码质量保证

### 代码审查清单
- [ ] 代码符合项目编码规范
- [ ] 函数和类有适当的文档注释
- [ ] 错误处理完整且合理
- [ ] 没有硬编码的魔法数字或字符串
- [ ] 测试覆盖率达到要求
- [ ] 性能考虑合理
- [ ] 安全漏洞检查通过
- [ ] 依赖项合理且必要

### 自动化检查
```bash
# Rust代码检查
cargo fmt --check
cargo clippy -- -D warnings
cargo test

# TypeScript代码检查
pnpm lint
pnpm type-check
pnpm test

# 安全检查
cargo audit
pnpm audit
```

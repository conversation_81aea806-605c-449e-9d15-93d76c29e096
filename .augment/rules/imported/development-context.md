---
type: "always_apply"
---

# Linux远程基线核查工具 - 开发上下文

## 项目当前状态

### 开发阶段
- **当前版本**: v1.0 开发阶段
- **完成状态**: 设计文档阶段完成，准备开始代码实现
- **下一步**: 项目初始化和核心模块开发

### 已完成工作
1. ✅ 项目架构设计文档
2. ✅ 数据库设计文档  
3. ✅ API接口设计文档
4. ✅ 开发环境搭建指南
5. ✅ 功能模块设计文档
6. ✅ 项目规则和开发规范

### 待完成工作
1. 🔄 Tauri项目初始化
2. 🔄 数据库模块实现
3. 🔄 SSH连接模块实现
4. 🔄 基线检查引擎实现
5. 🔄 前端界面开发
6. 🔄 集成测试和调试

## 技术决策记录

### 核心技术选择
- **应用框架**: Tauri 1.5+ (跨平台、性能优秀、安全性高)
- **前端框架**: React 18 + TypeScript (生态成熟、开发效率高)
- **后端语言**: Rust 1.70+ (内存安全、高性能)
- **数据库**: SQLite 3.40+ (轻量级、无需服务器)
- **UI组件库**: Ant Design 5.0+ (组件丰富、设计规范)
- **状态管理**: Zustand 4.0+ (轻量级、易用性好)
- **构建工具**: Vite + pnpm (快速构建、高效包管理)

### 架构决策
- **前后端分离**: 前端React + 后端Rust，通过Tauri Commands通信
- **模块化设计**: 按功能划分模块，便于维护和扩展
- **数据持久化**: 本地SQLite数据库，支持离线使用
- **安全设计**: 敏感数据加密存储，SSH连接安全管理

## 核心业务逻辑

### 主要功能流程

#### 1. 服务器连接流程
```
用户输入连接信息 → 前端验证 → 调用SSH连接命令 → 建立SSH连接 → 返回连接状态
```

#### 2. 基线核查流程
```
选择服务器和规则集 → 创建检查任务 → SSH执行命令 → 收集系统信息 → 规则匹配 → 生成报告 → 存储结果
```

#### 3. 结果查询流程
```
用户查询请求 → 数据库查询 → 结果格式化 → 前端展示 → 用户交互
```

### 数据模型关系
```
servers (1:N) check_tasks (1:N) check_results (N:1) check_rules (N:1) rule_sets
```

### 核心实体
- **Server**: 服务器连接信息
- **RuleSet**: 基线规则集
- **CheckRule**: 具体检查规则
- **CheckTask**: 检查任务
- **CheckResult**: 检查结果

## 开发优先级

### 第一阶段 (核心功能)
1. **项目初始化** - Tauri项目搭建和基础配置
2. **数据库模块** - SQLite集成和基础CRUD操作
3. **SSH连接模块** - SSH客户端实现和连接管理
4. **基础UI框架** - 主布局和导航组件

### 第二阶段 (业务功能)
1. **服务器管理** - 服务器CRUD和连接测试
2. **规则管理** - 内置规则集和规则展示
3. **基线检查引擎** - 规则执行和结果收集
4. **结果展示** - 检查结果表格和详情

### 第三阶段 (完善功能)
1. **数据导出** - 多格式报告导出
2. **系统配置** - 用户偏好和系统设置
3. **错误处理** - 完善的错误处理和用户提示
4. **性能优化** - 界面响应和查询优化

## 关键技术实现点

### SSH连接实现
- 使用`ssh2`库实现SSH客户端
- 支持密码和密钥认证
- 连接池管理和超时控制
- 命令执行和结果解析

### 基线规则引擎
- 规则解析和执行框架
- 命令输出验证机制
- 结果评分和风险计算
- 进度跟踪和任务管理

### 数据库设计
- 7个核心表的关系设计
- 索引优化和查询性能
- 数据迁移和版本管理
- 事务处理和数据一致性

### 前端状态管理
- Zustand全局状态管理
- 组件间数据传递
- 异步操作状态处理
- 实时进度更新

## 开发注意事项

### 安全考虑
- SSH连接信息加密存储
- 敏感数据脱敏处理
- 权限最小化原则
- 输入验证和SQL注入防护

### 性能考虑
- 大量数据的虚拟滚动
- 数据库查询优化
- 异步操作避免阻塞
- 内存使用控制

### 用户体验
- 友好的错误提示
- 操作进度反馈
- 响应式界面设计
- 跨平台一致性

### 可维护性
- 模块化代码结构
- 完善的错误处理
- 详细的代码注释
- 单元测试覆盖

## 测试策略

### 单元测试
- Rust模块单元测试
- React组件测试
- 数据库操作测试
- 工具函数测试

### 集成测试
- SSH连接集成测试
- 基线检查流程测试
- 数据库事务测试
- API接口测试

### 端到端测试
- 完整业务流程测试
- 跨平台兼容性测试
- 性能压力测试
- 用户界面测试

## 部署和发布

### 构建流程
- 开发环境构建验证
- 生产环境优化构建
- 跨平台构建测试
- 安装包制作和签名

### 发布流程
- 版本号管理
- 变更日志编写
- 发布包分发
- 用户文档更新

### 维护计划
- 定期安全更新
- 功能迭代开发
- 用户反馈处理
- 性能监控和优化

## 文档维护

### 技术文档
- API接口文档同步更新
- 数据库变更记录
- 架构决策文档
- 开发规范更新

### 用户文档
- 用户操作手册
- 安装部署指南
- 常见问题解答
- 最佳实践指南

## 团队协作

### 代码审查
- 所有代码变更需要审查
- 关注代码质量和规范
- 安全漏洞检查
- 性能影响评估

### 沟通机制
- 定期进度同步
- 技术难点讨论
- 需求变更评估
- 风险识别和应对

### 知识分享
- 技术方案分享
- 最佳实践总结
- 问题解决经验
- 工具使用技巧

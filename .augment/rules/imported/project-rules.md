---
type: "always_apply"
---

# Linux远程基线核查工具 - Augment项目规则

## 项目概述

这是一个基于Tauri框架开发的跨平台Linux远程基线核查工具，支持SSH连接到Linux服务器并执行安全基线核查。

### 核心信息
- **项目名称**: Linux远程基线核查工具 (Linux Remote Baseline Checker)
- **技术栈**: Tauri + React + TypeScript + Rust + SQLite
- **开发阶段**: 第一版开发中
- **目标平台**: Windows, macOS, Linux

## 项目结构规范

### 目录结构
```
baseline-checker/
├── src-tauri/          # Rust后端代码
│   ├── src/
│   │   ├── main.rs     # 主入口
│   │   ├── commands/   # Tauri命令模块
│   │   ├── database/   # 数据库操作模块
│   │   ├── ssh/        # SSH客户端模块
│   │   ├── baseline/   # 基线检查引擎
│   │   ├── models/     # 数据模型
│   │   ├── config/     # 配置管理
│   │   ├── security/   # 安全模块
│   │   └── errors/     # 错误处理
│   ├── Cargo.toml
│   ├── tauri.conf.json
│   └── migrations/     # 数据库迁移文件
├── src/                # React前端代码
│   ├── components/     # UI组件
│   │   ├── Layout/     # 布局组件
│   │   ├── Server/     # 服务器管理组件
│   │   ├── Baseline/   # 基线检查组件
│   │   └── Results/    # 结果展示组件
│   ├── pages/          # 页面组件
│   ├── hooks/          # 自定义Hooks
│   ├── stores/         # Zustand状态管理
│   ├── utils/          # 工具函数
│   ├── types/          # TypeScript类型定义
│   └── constants/      # 常量定义
├── docs/               # 项目文档
├── public/             # 静态资源
└── tests/              # 测试文件
```

## 开发规范

### 代码规范

#### Rust代码规范
- 使用`cargo fmt`格式化代码
- 使用`cargo clippy`进行代码检查
- 所有公共函数必须有文档注释
- 错误处理使用`Result<T, E>`类型
- 异步函数使用`async/await`
- 使用`thiserror`定义自定义错误类型

#### TypeScript代码规范
- 使用严格模式TypeScript配置
- 所有组件使用函数式组件
- 使用ESLint和Prettier进行代码格式化
- 接口和类型定义使用PascalCase
- 常量使用UPPER_SNAKE_CASE
- 文件名使用kebab-case

#### 命名约定
- **Rust模块**: snake_case (如: `ssh_client`, `database_manager`)
- **Rust结构体**: PascalCase (如: `SSHClient`, `CheckResult`)
- **Rust函数**: snake_case (如: `create_connection`, `execute_rule`)
- **TypeScript组件**: PascalCase (如: `ServerManagement`, `BaselineChecker`)
- **TypeScript接口**: PascalCase + Interface后缀 (如: `ServerInterface`, `TaskInterface`)
- **数据库表**: snake_case (如: `servers`, `check_tasks`)

### 数据库规范

#### 表命名规范
- 使用复数形式: `servers`, `check_tasks`, `check_results`
- 使用snake_case命名
- 主键统一使用`id`
- 外键使用`表名_id`格式
- 时间字段使用`created_at`, `updated_at`

#### 核心表结构
1. **servers** - 服务器信息管理
2. **rule_sets** - 基线规则集定义
3. **check_rules** - 具体检查规则
4. **check_tasks** - 检查任务记录
5. **check_results** - 检查结果存储
6. **system_config** - 系统配置
7. **operation_logs** - 操作日志

### API接口规范

#### Tauri Commands命名
- 使用snake_case命名
- 按功能模块分组
- 统一返回`Result<ApiResponse<T>, String>`格式

#### 接口分类
- **服务器管理**: `get_servers`, `create_server`, `update_server`, `delete_server`
- **基线核查**: `get_rule_sets`, `create_check_task`, `get_check_results`
- **系统配置**: `get_system_config`, `update_system_config`
- **文件操作**: `select_private_key_file`, `export_check_results`

### 状态管理规范

#### Zustand Store结构
```typescript
interface AppState {
  // 应用状态
  theme: 'light' | 'dark' | 'auto';
  language: string;
  
  // 业务状态
  servers: Server[];
  selectedServer: Server | null;
  currentTask: CheckTask | null;
  taskProgress: TaskProgress | null;
  
  // 操作方法
  actions: {
    setTheme: (theme: string) => void;
    setSelectedServer: (server: Server | null) => void;
    updateTaskProgress: (progress: TaskProgress) => void;
  };
}
```

## 功能模块规范

### 核心功能模块

#### 1. 连接管理模块 (SSH Connection)
- **职责**: SSH连接建立、管理、状态监控
- **核心文件**: `src-tauri/src/ssh/`
- **前端组件**: `src/components/Server/`

#### 2. 基线引擎模块 (Baseline Engine)
- **职责**: 规则执行、结果分析、系统信息收集
- **核心文件**: `src-tauri/src/baseline/`
- **前端组件**: `src/components/Baseline/`

#### 3. 数据管理模块 (Database)
- **职责**: SQLite操作、数据模型、查询优化
- **核心文件**: `src-tauri/src/database/`
- **迁移文件**: `src-tauri/migrations/`

#### 4. 用户界面模块 (UI)
- **职责**: 用户交互、数据展示、响应式布局
- **核心文件**: `src/components/`, `src/pages/`
- **状态管理**: `src/stores/`

#### 5. 配置管理模块 (Configuration)
- **职责**: 系统配置、用户偏好、默认值处理
- **核心文件**: `src-tauri/src/config/`
- **配置文件**: `tauri.conf.json`

### 内置基线规则

#### 支持的规则集
1. **CIS Ubuntu 20.04 Benchmark** - Ubuntu安全基线
2. **CIS CentOS 7 Benchmark** - CentOS安全基线
3. **Basic Security Check** - 通用安全检查

#### 规则分类
- **system** - 系统配置检查
- **network** - 网络安全检查
- **service** - 服务状态检查
- **file** - 文件权限检查
- **user** - 用户管理检查

## 开发流程规范

### Git工作流
1. 从`main`分支创建功能分支
2. 功能分支命名: `feature/功能名称` 或 `fix/问题描述`
3. 提交信息使用Conventional Commits格式
4. 代码审查后合并到`main`分支

### 提交信息格式
```
type(scope): description

[optional body]

[optional footer]
```

类型说明:
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建或工具相关

### 测试规范
- Rust单元测试覆盖率 > 80%
- 前端组件测试使用React Testing Library
- 集成测试覆盖主要业务流程
- 性能测试关注响应时间和内存使用

### 文档维护
- API变更必须更新接口文档
- 新功能需要更新用户手册
- 数据库变更需要更新数据库文档
- 重要决策记录在ADR文档中

## 安全规范

### 数据安全
- 敏感数据（密码、私钥）必须加密存储
- SSH连接信息使用AES-256-GCM加密
- 数据库访问使用参数化查询防止SQL注入
- 日志记录时对敏感信息进行脱敏

### 连接安全
- 支持SSH密钥认证，优先使用密钥认证
- 连接超时设置，防止长时间占用资源
- 连接失败重试机制，避免暴力破解
- 会话管理，及时清理无效连接

### 应用安全
- Tauri安全配置，限制API访问权限
- CSP策略设置，防止XSS攻击
- 权限最小化原则，只授予必要权限
- 定期安全审计和漏洞扫描

## 性能规范

### 响应时间要求
- 服务器列表查询: < 100ms
- SSH连接测试: < 5s
- 基线核查: 平均每规则 < 2s
- 结果查询: < 200ms
- 数据导出: < 10s (1000条记录)

### 资源使用限制
- 内存使用: < 500MB (正常运行)
- CPU使用: < 50% (核查执行时)
- 磁盘空间: 数据库文件 < 100MB
- 网络带宽: SSH连接 < 1MB/s

### 优化策略
- 前端虚拟滚动处理大量数据
- 数据库查询使用索引优化
- SSH连接池复用连接
- 异步处理避免界面阻塞

## 部署规范

### 构建配置
- 生产构建启用优化选项
- 代码签名和公证（macOS）
- 安装包制作和分发
- 自动更新机制配置

### 版本管理
- 语义化版本号 (Semantic Versioning)
- 版本发布说明和变更日志
- 向后兼容性保证
- 数据库迁移脚本

### 监控和日志
- 应用崩溃报告收集
- 性能指标监控
- 用户行为分析
- 错误日志分级记录

## 质量保证

### 代码质量
- 代码审查必须通过
- 自动化测试必须通过
- 代码覆盖率达标
- 静态分析工具检查

### 用户体验
- 界面响应速度测试
- 跨平台兼容性测试
- 用户操作流程测试
- 错误处理友好性测试

### 文档质量
- 技术文档准确性
- 用户手册完整性
- API文档同步性
- 示例代码可运行性
